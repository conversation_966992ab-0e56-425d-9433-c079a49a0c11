"""
电商客服用户信息分析模块
提供全面的用户行为分析、需求分析和服务质量分析
"""

import sys
import os
import json
import pandas as pd
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from message_prossor.utils import load_json, spilit_log_message
from message_prossor.jieba_utils import jieba_optimized_common_substring

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class CustomerAnalyzer:
    """电商客服用户信息分析器"""
    
    def __init__(self, data_path: str):
        """
        初始化分析器
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.raw_data = None
        self.log_data = None
        self.error_data = None
        self.analysis_results = {}
        
    def load_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        self.raw_data = load_json.load_jsonl(self.data_path)
        self.log_data, self.error_data = spilit_log_message.split_log_pre_message(self.raw_data)
        print(f"成功加载 {len(self.log_data)} 条有效对话数据")
        
    def analyze_user_behavior(self) -> Dict[str, Any]:
        """分析用户行为模式"""
        print("正在分析用户行为模式...")
        
        # 1. 用户活跃度分析
        user_activity = self._analyze_user_activity()
        
        # 2. 咨询商品分布
        product_distribution = self._analyze_product_distribution()
        
        # 3. 用户意图分析
        intent_analysis = self._analyze_user_intents()
        
        # 4. 对话时长分析
        conversation_patterns = self._analyze_conversation_patterns()
        
        behavior_results = {
            'user_activity': user_activity,
            'product_distribution': product_distribution,
            'intent_analysis': intent_analysis,
            'conversation_patterns': conversation_patterns
        }
        
        self.analysis_results['user_behavior'] = behavior_results
        return behavior_results
    
    def analyze_service_quality(self) -> Dict[str, Any]:
        """分析服务质量"""
        print("正在分析服务质量...")
        
        # 1. 响应时间分析
        response_time_analysis = self._analyze_response_times()
        
        # 2. 问题解决率
        resolution_rate = self._analyze_resolution_rate()
        
        # 3. 用户满意度指标
        satisfaction_metrics = self._analyze_satisfaction_metrics()
        
        # 4. 常见问题识别
        common_issues = self._identify_common_issues()
        
        quality_results = {
            'response_time': response_time_analysis,
            'resolution_rate': resolution_rate,
            'satisfaction_metrics': satisfaction_metrics,
            'common_issues': common_issues
        }
        
        self.analysis_results['service_quality'] = quality_results
        return quality_results
    
    def analyze_business_insights(self) -> Dict[str, Any]:
        """分析业务洞察"""
        print("正在分析业务洞察...")
        
        # 1. 热门商品分析
        popular_products = self._analyze_popular_products()
        
        # 2. 用户需求趋势
        demand_trends = self._analyze_demand_trends()
        
        # 3. 转化率分析
        conversion_analysis = self._analyze_conversion_rates()
        
        # 4. 客户价值分析
        customer_value = self._analyze_customer_value()
        
        business_results = {
            'popular_products': popular_products,
            'demand_trends': demand_trends,
            'conversion_analysis': conversion_analysis,
            'customer_value': customer_value
        }
        
        self.analysis_results['business_insights'] = business_results
        return business_results
    
    def _analyze_user_activity(self) -> Dict[str, Any]:
        """分析用户活跃度"""
        user_stats = defaultdict(int)
        time_distribution = defaultdict(int)
        
        for item in self.log_data:
            try:
                # 用户活跃度统计
                buyer_nick = item['context']['buyerNick']
                user_stats[buyer_nick] += 1
                
                # 时间分布分析
                timestamp = item['__time__']
                hour = datetime.fromtimestamp(int(timestamp)).hour
                time_distribution[hour] += 1
                
            except (KeyError, ValueError) as e:
                continue
        
        # 活跃用户排行
        top_users = dict(Counter(user_stats).most_common(20))
        
        # 时间分布
        hourly_distribution = dict(sorted(time_distribution.items()))
        
        return {
            'total_users': len(user_stats),
            'total_conversations': sum(user_stats.values()),
            'avg_conversations_per_user': sum(user_stats.values()) / len(user_stats) if user_stats else 0,
            'top_active_users': top_users,
            'hourly_distribution': hourly_distribution
        }
    
    def _analyze_product_distribution(self) -> Dict[str, Any]:
        """分析咨询商品分布"""
        product_counts = defaultdict(int)

        for item in self.log_data:
            try:
                # 从响应中提取商品信息
                response_data = item['message']['response']
                if isinstance(response_data, str):
                    response_data = json.loads(response_data)
                product = response_data.get('咨询的商品', '无')
                product_counts[product] += 1
            except (KeyError, json.JSONDecodeError, TypeError) as e:
                product_counts['未知'] += 1

        total_consultations = sum(product_counts.values())
        product_percentages = {
            product: (count / total_consultations * 100)
            for product, count in product_counts.items()
        }

        return {
            'product_counts': dict(product_counts),
            'product_percentages': product_percentages,
            'total_consultations': total_consultations
        }
    
    def _analyze_user_intents(self) -> Dict[str, Any]:
        """分析用户意图"""
        intent_patterns = defaultdict(int)
        user_requests = []
        
        for item in self.log_data:
            try:
                user_request = item['message']['user_request']
                if user_request:
                    user_requests.append(user_request)
                    
                    # 简单的意图分类
                    request_lower = user_request.lower()
                    if any(word in request_lower for word in ['人工', '客服']):
                        intent_patterns['寻求人工客服'] += 1
                    elif any(word in request_lower for word in ['价格', '多少钱', '费用']):
                        intent_patterns['价格咨询'] += 1
                    elif any(word in request_lower for word in ['怎么', '如何', '流程']):
                        intent_patterns['操作咨询'] += 1
                    elif any(word in request_lower for word in ['退货', '退款', '售后']):
                        intent_patterns['售后服务'] += 1
                    else:
                        intent_patterns['其他咨询'] += 1
                        
            except (KeyError, TypeError) as e:
                continue
        
        return {
            'intent_distribution': dict(intent_patterns),
            'total_requests': len(user_requests),
            'sample_requests': user_requests[:10]  # 样本请求
        }
    
    def _analyze_conversation_patterns(self) -> Dict[str, Any]:
        """分析对话模式"""
        conversation_lengths = []
        chat_sessions = defaultdict(list)
        
        # 按聊天ID分组
        for item in self.log_data:
            try:
                chat_id = item['context']['chatId']
                timestamp = int(item['__time__'])
                chat_sessions[chat_id].append(timestamp)
            except (KeyError, ValueError) as e:
                continue
        
        # 分析每个会话
        session_durations = []
        for chat_id, timestamps in chat_sessions.items():
            if len(timestamps) > 1:
                timestamps.sort()
                duration = timestamps[-1] - timestamps[0]
                session_durations.append(duration)
            conversation_lengths.append(len(timestamps))
        
        return {
            'total_sessions': len(chat_sessions),
            'avg_messages_per_session': sum(conversation_lengths) / len(conversation_lengths) if conversation_lengths else 0,
            'avg_session_duration_seconds': sum(session_durations) / len(session_durations) if session_durations else 0,
            'max_session_length': max(conversation_lengths) if conversation_lengths else 0,
            'min_session_length': min(conversation_lengths) if conversation_lengths else 0
        }
    
    def _analyze_response_times(self) -> Dict[str, Any]:
        """分析响应时间（模拟数据，实际需要更详细的时间戳）"""
        # 这里是示例实现，实际需要根据具体的时间戳数据来计算
        return {
            'avg_response_time_seconds': 30,  # 示例数据
            'median_response_time_seconds': 25,
            'response_time_distribution': {
                '0-10s': 0.3,
                '10-30s': 0.4,
                '30-60s': 0.2,
                '60s+': 0.1
            }
        }
    
    def _analyze_resolution_rate(self) -> Dict[str, Any]:
        """分析问题解决率"""
        resolved_indicators = ['谢谢', '好的', '明白了', '解决了', 'ok', '可以']
        total_conversations = len(self.log_data)
        resolved_count = 0
        
        for item in self.log_data:
            try:
                user_request = item['message']['user_request'].lower()
                if any(indicator in user_request for indicator in resolved_indicators):
                    resolved_count += 1
            except (KeyError, AttributeError) as e:
                continue
        
        resolution_rate = resolved_count / total_conversations if total_conversations > 0 else 0
        
        return {
            'total_conversations': total_conversations,
            'resolved_conversations': resolved_count,
            'resolution_rate': resolution_rate,
            'resolution_percentage': resolution_rate * 100
        }
    
    def _analyze_satisfaction_metrics(self) -> Dict[str, Any]:
        """分析用户满意度指标"""
        positive_words = ['谢谢', '好的', '满意', '不错', '很好']
        negative_words = ['不满意', '差', '不好', '问题', '投诉']
        
        positive_count = 0
        negative_count = 0
        neutral_count = 0
        
        for item in self.log_data:
            try:
                user_request = item['message']['user_request'].lower()
                if any(word in user_request for word in positive_words):
                    positive_count += 1
                elif any(word in user_request for word in negative_words):
                    negative_count += 1
                else:
                    neutral_count += 1
            except (KeyError, AttributeError) as e:
                neutral_count += 1
        
        total = positive_count + negative_count + neutral_count
        
        return {
            'positive_feedback': positive_count,
            'negative_feedback': negative_count,
            'neutral_feedback': neutral_count,
            'satisfaction_score': positive_count / total if total > 0 else 0,
            'sentiment_distribution': {
                'positive': positive_count / total if total > 0 else 0,
                'negative': negative_count / total if total > 0 else 0,
                'neutral': neutral_count / total if total > 0 else 0
            }
        }
    
    def _identify_common_issues(self) -> Dict[str, Any]:
        """识别常见问题"""
        user_requests = []
        for item in self.log_data:
            try:
                user_request = item['message']['user_request']
                if user_request and len(user_request.strip()) > 0:
                    user_requests.append(user_request)
            except (KeyError, TypeError) as e:
                continue
        
        # 使用现有的关键词提取功能
        try:
            enhanced_data, word_frequency = jieba_optimized_common_substring.extract_keywords_from_pre_user_data(
                self.log_data, sample_rate=0.1, min_freq=3
            )
            
            # 获取高频关键词
            top_keywords = dict(word_frequency.most_common(20))
            
        except Exception as e:
            print(f"关键词提取失败: {e}")
            top_keywords = {}
        
        return {
            'total_unique_requests': len(set(user_requests)),
            'top_keywords': top_keywords,
            'sample_requests': user_requests[:20]
        }

    def _analyze_popular_products(self) -> Dict[str, Any]:
        """分析热门商品"""
        product_mentions = defaultdict(int)
        product_keywords = ['照片', '证件照', '名片', '拍立得', '电子版', '修图']

        for item in self.log_data:
            try:
                user_request = item['message']['user_request'].lower()
                for keyword in product_keywords:
                    if keyword in user_request:
                        product_mentions[keyword] += 1
            except (KeyError, AttributeError) as e:
                continue

        return {
            'product_mentions': dict(product_mentions),
            'most_popular_product': max(product_mentions.items(), key=lambda x: x[1]) if product_mentions else None
        }

    def _analyze_demand_trends(self) -> Dict[str, Any]:
        """分析需求趋势"""
        daily_requests = defaultdict(int)

        for item in self.log_data:
            try:
                timestamp = int(item['__time__'])
                date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                daily_requests[date] += 1
            except (KeyError, ValueError) as e:
                continue

        # 计算趋势
        sorted_dates = sorted(daily_requests.items())
        if len(sorted_dates) > 1:
            recent_avg = sum(count for _, count in sorted_dates[-7:]) / min(7, len(sorted_dates))
            earlier_avg = sum(count for _, count in sorted_dates[:-7]) / max(1, len(sorted_dates) - 7)
            trend = (recent_avg - earlier_avg) / earlier_avg if earlier_avg > 0 else 0
        else:
            trend = 0

        return {
            'daily_requests': dict(daily_requests),
            'trend_percentage': trend * 100,
            'peak_day': max(daily_requests.items(), key=lambda x: x[1]) if daily_requests else None
        }

    def _analyze_conversion_rates(self) -> Dict[str, Any]:
        """分析转化率"""
        purchase_indicators = ['下单', '购买', '付款', '拍下', '买']
        total_inquiries = len(self.log_data)
        purchase_mentions = 0

        for item in self.log_data:
            try:
                user_request = item['message']['user_request'].lower()
                if any(indicator in user_request for indicator in purchase_indicators):
                    purchase_mentions += 1
            except (KeyError, AttributeError) as e:
                continue

        conversion_rate = purchase_mentions / total_inquiries if total_inquiries > 0 else 0

        return {
            'total_inquiries': total_inquiries,
            'purchase_mentions': purchase_mentions,
            'conversion_rate': conversion_rate,
            'conversion_percentage': conversion_rate * 100
        }

    def _analyze_customer_value(self) -> Dict[str, Any]:
        """分析客户价值"""
        user_engagement = defaultdict(int)
        repeat_customers = 0

        for item in self.log_data:
            try:
                buyer_nick = item['context']['buyerNick']
                user_engagement[buyer_nick] += 1
            except KeyError as e:
                continue

        # 计算回头客
        for user, count in user_engagement.items():
            if count > 1:
                repeat_customers += 1

        total_users = len(user_engagement)
        repeat_rate = repeat_customers / total_users if total_users > 0 else 0

        # 高价值客户（多次咨询的用户）
        high_value_customers = {
            user: count for user, count in user_engagement.items()
            if count >= 3
        }

        return {
            'total_unique_customers': total_users,
            'repeat_customers': repeat_customers,
            'repeat_customer_rate': repeat_rate,
            'repeat_rate_percentage': repeat_rate * 100,
            'high_value_customers': high_value_customers,
            'avg_interactions_per_customer': sum(user_engagement.values()) / total_users if total_users > 0 else 0
        }

    def generate_visualizations(self, output_dir: str = "customer_analysis_output"):
        """生成可视化图表"""
        print("正在生成可视化图表...")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 1. 用户活跃度时间分布图
        if 'user_behavior' in self.analysis_results:
            self._plot_hourly_distribution(output_dir)
            self._plot_product_distribution(output_dir)
            self._plot_intent_distribution(output_dir)

        # 2. 服务质量指标图
        if 'service_quality' in self.analysis_results:
            self._plot_satisfaction_metrics(output_dir)

        # 3. 业务洞察图
        if 'business_insights' in self.analysis_results:
            self._plot_demand_trends(output_dir)

        print(f"可视化图表已保存到: {output_dir}")

    def _plot_hourly_distribution(self, output_dir: str):
        """绘制小时分布图"""
        hourly_data = self.analysis_results['user_behavior']['user_activity']['hourly_distribution']

        plt.figure(figsize=(12, 6))
        hours = list(hourly_data.keys())
        counts = list(hourly_data.values())

        plt.bar(hours, counts, color='skyblue', alpha=0.7)
        plt.title('用户咨询时间分布', fontsize=16, fontweight='bold')
        plt.xlabel('小时', fontsize=12)
        plt.ylabel('咨询次数', fontsize=12)
        plt.xticks(range(0, 24, 2))
        plt.grid(axis='y', alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'hourly_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_product_distribution(self, output_dir: str):
        """绘制商品咨询分布图"""
        product_data = self.analysis_results['user_behavior']['product_distribution']['product_counts']

        plt.figure(figsize=(10, 8))
        products = list(product_data.keys())
        counts = list(product_data.values())

        colors = plt.cm.Set3(range(len(products)))
        plt.pie(counts, labels=products, autopct='%1.1f%%', colors=colors, startangle=90)
        plt.title('咨询商品分布', fontsize=16, fontweight='bold')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'product_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_intent_distribution(self, output_dir: str):
        """绘制用户意图分布图"""
        intent_data = self.analysis_results['user_behavior']['intent_analysis']['intent_distribution']

        plt.figure(figsize=(12, 6))
        intents = list(intent_data.keys())
        counts = list(intent_data.values())

        plt.bar(intents, counts, color='lightcoral', alpha=0.7)
        plt.title('用户意图分布', fontsize=16, fontweight='bold')
        plt.xlabel('意图类型', fontsize=12)
        plt.ylabel('次数', fontsize=12)
        plt.xticks(rotation=45)
        plt.grid(axis='y', alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'intent_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_satisfaction_metrics(self, output_dir: str):
        """绘制满意度指标图"""
        sentiment_data = self.analysis_results['service_quality']['satisfaction_metrics']['sentiment_distribution']

        plt.figure(figsize=(8, 8))
        labels = list(sentiment_data.keys())
        sizes = list(sentiment_data.values())
        colors = ['lightgreen', 'lightcoral', 'lightgray']

        plt.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
        plt.title('用户情感分布', fontsize=16, fontweight='bold')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'satisfaction_metrics.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_demand_trends(self, output_dir: str):
        """绘制需求趋势图"""
        daily_data = self.analysis_results['business_insights']['demand_trends']['daily_requests']

        if not daily_data:
            return

        plt.figure(figsize=(14, 6))
        dates = sorted(daily_data.keys())
        counts = [daily_data[date] for date in dates]

        plt.plot(dates, counts, marker='o', linewidth=2, markersize=6, color='steelblue')
        plt.title('每日咨询量趋势', fontsize=16, fontweight='bold')
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('咨询次数', fontsize=12)
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'demand_trends.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def generate_report(self, output_dir: str = "customer_analysis_output") -> str:
        """生成分析报告"""
        print("正在生成分析报告...")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        report_content = self._create_report_content()

        # 保存报告
        report_path = os.path.join(output_dir, 'customer_analysis_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # 保存JSON格式的详细数据
        json_path = os.path.join(output_dir, 'analysis_results.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)

        print(f"分析报告已保存到: {report_path}")
        print(f"详细数据已保存到: {json_path}")

        return report_path

    def _create_report_content(self) -> str:
        """创建报告内容"""
        report = "# 电商客服用户信息分析报告\n\n"
        report += f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # 1. 概览
        report += "## 📊 数据概览\n\n"
        if 'user_behavior' in self.analysis_results:
            user_activity = self.analysis_results['user_behavior']['user_activity']
            report += f"- **总用户数**: {user_activity['total_users']}\n"
            report += f"- **总对话数**: {user_activity['total_conversations']}\n"
            report += f"- **平均每用户对话数**: {user_activity['avg_conversations_per_user']:.2f}\n\n"

        # 2. 用户行为分析
        if 'user_behavior' in self.analysis_results:
            report += "## 👥 用户行为分析\n\n"

            # 商品咨询分布
            product_dist = self.analysis_results['user_behavior']['product_distribution']
            report += "### 商品咨询分布\n\n"
            for product, percentage in product_dist['product_percentages'].items():
                report += f"- **{product}**: {percentage:.1f}%\n"
            report += "\n"

            # 用户意图分析
            intent_analysis = self.analysis_results['user_behavior']['intent_analysis']
            report += "### 用户意图分布\n\n"
            for intent, count in intent_analysis['intent_distribution'].items():
                report += f"- **{intent}**: {count} 次\n"
            report += "\n"

            # 对话模式
            conv_patterns = self.analysis_results['user_behavior']['conversation_patterns']
            report += "### 对话模式\n\n"
            report += f"- **总会话数**: {conv_patterns['total_sessions']}\n"
            report += f"- **平均每会话消息数**: {conv_patterns['avg_messages_per_session']:.2f}\n"
            report += f"- **平均会话时长**: {conv_patterns['avg_session_duration_seconds']:.0f} 秒\n\n"

        # 3. 服务质量分析
        if 'service_quality' in self.analysis_results:
            report += "## 🎯 服务质量分析\n\n"

            # 问题解决率
            resolution = self.analysis_results['service_quality']['resolution_rate']
            report += "### 问题解决率\n\n"
            report += f"- **解决率**: {resolution['resolution_percentage']:.1f}%\n"
            report += f"- **已解决对话**: {resolution['resolved_conversations']}\n"
            report += f"- **总对话数**: {resolution['total_conversations']}\n\n"

            # 用户满意度
            satisfaction = self.analysis_results['service_quality']['satisfaction_metrics']
            report += "### 用户满意度\n\n"
            report += f"- **满意度评分**: {satisfaction['satisfaction_score']:.2f}\n"
            sentiment_dist = satisfaction['sentiment_distribution']
            report += f"- **正面反馈**: {sentiment_dist['positive']:.1%}\n"
            report += f"- **负面反馈**: {sentiment_dist['negative']:.1%}\n"
            report += f"- **中性反馈**: {sentiment_dist['neutral']:.1%}\n\n"

            # 常见问题
            common_issues = self.analysis_results['service_quality']['common_issues']
            report += "### 高频关键词 (Top 10)\n\n"
            top_keywords = list(common_issues['top_keywords'].items())[:10]
            for keyword, freq in top_keywords:
                report += f"- **{keyword}**: {freq} 次\n"
            report += "\n"

        # 4. 业务洞察
        if 'business_insights' in self.analysis_results:
            report += "## 💼 业务洞察\n\n"

            # 热门商品
            popular_products = self.analysis_results['business_insights']['popular_products']
            report += "### 热门商品关键词\n\n"
            for product, mentions in popular_products['product_mentions'].items():
                report += f"- **{product}**: {mentions} 次提及\n"
            report += "\n"

            # 需求趋势
            demand_trends = self.analysis_results['business_insights']['demand_trends']
            report += "### 需求趋势\n\n"
            report += f"- **趋势变化**: {demand_trends['trend_percentage']:.1f}%\n"
            if demand_trends['peak_day']:
                peak_day, peak_count = demand_trends['peak_day']
                report += f"- **峰值日期**: {peak_day} ({peak_count} 次咨询)\n"
            report += "\n"

            # 转化率分析
            conversion = self.analysis_results['business_insights']['conversion_analysis']
            report += "### 转化率分析\n\n"
            report += f"- **转化率**: {conversion['conversion_percentage']:.1f}%\n"
            report += f"- **购买意向提及**: {conversion['purchase_mentions']} 次\n"
            report += f"- **总咨询数**: {conversion['total_inquiries']}\n\n"

            # 客户价值
            customer_value = self.analysis_results['business_insights']['customer_value']
            report += "### 客户价值分析\n\n"
            report += f"- **回头客比例**: {customer_value['repeat_rate_percentage']:.1f}%\n"
            report += f"- **平均互动次数**: {customer_value['avg_interactions_per_customer']:.2f}\n"
            report += f"- **高价值客户数**: {len(customer_value['high_value_customers'])}\n\n"

        # 5. 建议和改进方向
        report += "## 💡 建议和改进方向\n\n"
        report += self._generate_recommendations()

        return report

    def _generate_recommendations(self) -> str:
        """生成改进建议"""
        recommendations = ""

        # 基于分析结果生成建议
        if 'service_quality' in self.analysis_results:
            satisfaction = self.analysis_results['service_quality']['satisfaction_metrics']
            if satisfaction['satisfaction_score'] < 0.7:
                recommendations += "### 🔧 服务质量改进\n"
                recommendations += "- 提高客服响应质量，关注用户反馈\n"
                recommendations += "- 加强客服培训，提升问题解决能力\n\n"

        if 'business_insights' in self.analysis_results:
            conversion = self.analysis_results['business_insights']['conversion_analysis']
            if conversion['conversion_rate'] < 0.1:
                recommendations += "### 📈 转化率优化\n"
                recommendations += "- 优化产品介绍和价格策略\n"
                recommendations += "- 提供更多购买激励措施\n"
                recommendations += "- 简化下单流程\n\n"

            customer_value = self.analysis_results['business_insights']['customer_value']
            if customer_value['repeat_rate_percentage'] < 30:
                recommendations += "### 🔄 客户留存提升\n"
                recommendations += "- 建立客户关系管理系统\n"
                recommendations += "- 提供个性化服务和推荐\n"
                recommendations += "- 实施客户忠诚度计划\n\n"

        if 'user_behavior' in self.analysis_results:
            hourly_dist = self.analysis_results['user_behavior']['user_activity']['hourly_distribution']
            peak_hours = sorted(hourly_dist.items(), key=lambda x: x[1], reverse=True)[:3]
            recommendations += "### ⏰ 服务时间优化\n"
            recommendations += f"- 在高峰时段 ({', '.join([str(h[0]) for h in peak_hours])}) 增加客服人员\n"
            recommendations += "- 考虑24小时客服或智能客服系统\n\n"

        if not recommendations:
            recommendations = "- 继续保持当前服务水平\n"
            recommendations += "- 定期监控关键指标变化\n"
            recommendations += "- 收集更多用户反馈数据\n"

        return recommendations

    def run_complete_analysis(self, output_dir: str = "customer_analysis_output") -> str:
        """运行完整的分析流程"""
        print("开始电商客服用户信息分析...")

        # 1. 加载数据
        self.load_data()

        # 2. 执行各项分析
        self.analyze_user_behavior()
        self.analyze_service_quality()
        self.analyze_business_insights()

        # 3. 生成可视化图表
        self.generate_visualizations(output_dir)

        # 4. 生成分析报告
        report_path = self.generate_report(output_dir)

        print("✅ 分析完成！")
        print(f"📁 结果保存在: {output_dir}")
        print(f"📄 报告文件: {report_path}")

        return output_dir


def main():
    """主函数示例"""
    # 使用示例
    analyzer = CustomerAnalyzer('data/807凌晨改写log.json')

    # 运行完整分析
    output_dir = analyzer.run_complete_analysis()

    print(f"\n分析结果已保存到: {output_dir}")
    print("包含以下文件:")
    print("- customer_analysis_report.md (分析报告)")
    print("- analysis_results.json (详细数据)")
    print("- *.png (可视化图表)")


if __name__ == "__main__":
    main()
