# 电商客服用户信息分析系统

## 📋 概述

这是一个专门为电商客服场景设计的用户信息分析系统，能够从客服对话数据中提取有价值的用户行为洞察、服务质量指标和业务智能。

## 🎯 主要功能

### 1. 用户行为分析
- **用户活跃度分析**: 统计用户数量、对话频次、活跃时间分布
- **商品咨询分布**: 分析用户对不同商品的咨询比例
- **用户意图识别**: 自动识别用户咨询意图（价格、操作、售后等）
- **对话模式分析**: 分析对话长度、会话时长等模式

### 2. 服务质量分析
- **响应时间分析**: 评估客服响应效率
- **问题解决率**: 统计问题解决情况
- **用户满意度**: 基于用户反馈分析满意度
- **常见问题识别**: 提取高频关键词和问题

### 3. 业务洞察分析
- **热门商品分析**: 识别用户关注的热门商品
- **需求趋势分析**: 分析咨询量的时间趋势
- **转化率分析**: 评估咨询到购买的转化情况
- **客户价值分析**: 识别高价值客户和回头客

## 🚀 快速开始

### 安装依赖

```bash
pip install pandas matplotlib seaborn jieba
```

### 基本使用

```python
from customer_analysis import CustomerAnalyzer

# 创建分析器
analyzer = CustomerAnalyzer('data/807凌晨改写log.json')

# 运行完整分析
output_dir = analyzer.run_complete_analysis()

print(f"分析结果保存在: {output_dir}")
```

### 使用主程序

```bash
cd pipeline
python run_customer_analysis.py
```

## 📊 输出结果

分析完成后会生成以下文件：

### 1. 分析报告
- `customer_analysis_report.md`: 详细的分析报告（Markdown格式）
- `analysis_results.json`: 完整的分析数据（JSON格式）

### 2. 可视化图表
- `hourly_distribution.png`: 用户咨询时间分布图
- `product_distribution.png`: 商品咨询分布饼图
- `intent_distribution.png`: 用户意图分布柱状图
- `satisfaction_metrics.png`: 用户满意度分布图
- `demand_trends.png`: 需求趋势折线图

## 🔧 高级用法

### 1. 自定义分析参数

```python
analyzer = CustomerAnalyzer('your_data.json')

# 单独运行各项分析
user_behavior = analyzer.analyze_user_behavior()
service_quality = analyzer.analyze_service_quality()
business_insights = analyzer.analyze_business_insights()

# 自定义输出目录
analyzer.generate_visualizations('custom_output_dir')
analyzer.generate_report('custom_output_dir')
```

### 2. 比较多个数据集

```python
datasets = ['data1.json', 'data2.json', 'data3.json']
results = []

for dataset in datasets:
    analyzer = CustomerAnalyzer(dataset)
    analyzer.load_data()
    analyzer.analyze_user_behavior()
    # ... 其他分析
    results.append(analyzer.analysis_results)

# 比较结果
```

### 3. 时间段过滤

```python
# 可以在数据加载后进行时间过滤
analyzer.load_data()

# 过滤最近7天的数据
from datetime import datetime, timedelta
recent_data = []
cutoff_time = datetime.now() - timedelta(days=7)

for item in analyzer.log_data:
    timestamp = int(item['__time__'])
    if datetime.fromtimestamp(timestamp) >= cutoff_time:
        recent_data.append(item)

analyzer.log_data = recent_data
# 继续分析...
```

## 📈 关键指标说明

### 用户行为指标
- **总用户数**: 去重后的用户数量
- **总对话数**: 所有对话记录数
- **平均每用户对话数**: 用户平均咨询频次
- **活跃时间分布**: 各时段的咨询量分布

### 服务质量指标
- **问题解决率**: 基于用户反馈计算的解决比例
- **用户满意度**: 基于情感分析的满意度评分
- **响应时间**: 客服响应的平均时间

### 业务指标
- **转化率**: 从咨询到购买意向的转化比例
- **回头客比例**: 多次咨询的用户占比
- **需求趋势**: 咨询量的时间变化趋势

## 🎨 自定义分析

### 添加新的分析维度

```python
class CustomAnalyzer(CustomerAnalyzer):
    def analyze_custom_metrics(self):
        """自定义分析方法"""
        # 添加你的分析逻辑
        pass
    
    def _custom_visualization(self, output_dir):
        """自定义可视化"""
        # 添加你的图表生成逻辑
        pass
```

### 修改意图识别规则

在 `_analyze_user_intents` 方法中修改关键词匹配规则：

```python
# 自定义意图识别关键词
intent_keywords = {
    '价格咨询': ['价格', '多少钱', '费用', '成本'],
    '技术支持': ['怎么用', '如何', '教程', '步骤'],
    '售后服务': ['退货', '退款', '换货', '质量问题'],
    # 添加更多意图类别...
}
```

## 🔍 数据格式要求

输入数据应为JSON Lines格式，每行包含：

```json
{
  "__time__": "1754496011",
  "context": {
    "buyerNick": "用户昵称",
    "chatId": "会话ID",
    "source": "平台来源"
  },
  "message": {
    "user_request": "用户请求内容",
    "response": "客服响应内容"
  }
}
```

## 🛠️ 故障排除

### 常见问题

1. **数据加载失败**
   - 检查文件路径是否正确
   - 确认数据格式符合要求
   - 查看错误日志了解具体问题

2. **图表生成失败**
   - 确保安装了matplotlib和seaborn
   - 检查中文字体设置
   - 确认输出目录有写入权限

3. **分析结果异常**
   - 检查数据质量和完整性
   - 确认时间戳格式正确
   - 验证必要字段是否存在

### 性能优化

- 对于大数据集，可以使用采样分析
- 调整关键词提取的采样率参数
- 使用多进程处理大量数据

## 📞 技术支持

如果遇到问题或需要定制化功能，请：

1. 查看错误日志和输出信息
2. 检查数据格式和质量
3. 参考示例代码和文档
4. 提交详细的问题描述

## 🔄 更新日志

- **v1.0.0**: 初始版本，包含基础分析功能
- 支持用户行为、服务质量、业务洞察三大分析模块
- 提供可视化图表和详细报告
- 支持多数据集对比分析
