"""
K-means聚类分析使用示例
演示如何使用自定义输出路径
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from message_prossor.utils import (
    load_json,
    spilit_log_message
)

def example_with_custom_output():
    """
    示例：使用自定义输出路径进行聚类分析
    """
    print("=== K-means聚类分析示例 ===")
    
    # 1. 创建自定义输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    custom_output = f"my_clustering_analysis_{timestamp}"
    
    print(f"将使用输出目录: {custom_output}")
    
    # 2. 尝试导入完整版模块
    try:
        from run_kmeans_analysis import main as run_full_analysis
        print("使用完整版K-means聚类分析...")
        
        # 运行完整版分析
        output_dir = run_full_analysis(custom_output,'data/807凌晨改写log.json')
        print(f"完整版分析完成，结果保存在: {output_dir}")
        
    except ImportError as e:
        print(f"完整版模块导入失败: {e}")
        print("尝试使用简化版...")
        
        try:
            from simple_kmeans import main as run_simple_analysis
            print("使用简化版K-means聚类分析...")
            
            # 运行简化版分析
            output_dir = run_simple_analysis(custom_output)
            print(f"简化版分析完成，结果保存在: {output_dir}")
            
        except ImportError as e2:
            print(f"简化版模块也导入失败: {e2}")
            print("请检查依赖包是否正确安装")
            return None
    
    return custom_output

def example_with_specific_k():
    """
    示例：使用特定的k值进行聚类
    """
    print("\n=== 使用特定k值的聚类示例 ===")
    
    try:
        from kmeans_clustering import perform_kmeans_clustering, print_cluster_analysis
        
        # 加载数据
        print("加载数据...")
        data = load_json.load_jsonl('./data/阿里云改写日志.json')
        log_data, error_data = spilit_log_message.split_log_pre_message(data)
        
        if len(log_data) == 0:
            print("没有有效数据")
            return
        
        # 创建输出目录
        output_dir = "specific_k_analysis"
        os.makedirs(output_dir, exist_ok=True)
        
        # 使用特定的k值进行聚类
        k_value = 6
        print(f"使用k={k_value}进行聚类...")
        
        results = perform_kmeans_clustering(
            log_data, 
            n_clusters=k_value, 
            max_features=500,  # 减少特征数以加快速度
            random_state=42
        )
        
        # 打印分析结果
        print_cluster_analysis(results)
        
        # 保存结果
        results_df = results['results_df']
        csv_path = os.path.join(output_dir, f'clustering_k{k_value}_results.csv')
        results_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {csv_path}")
        
    except ImportError as e:
        print(f"无法导入完整版模块: {e}")
        print("请安装必要的依赖包或使用简化版")

def example_compare_different_k():
    """
    示例：比较不同k值的聚类效果
    """
    print("\n=== 比较不同k值的聚类效果 ===")
    
    try:
        from kmeans_clustering import perform_kmeans_clustering
        
        # 加载数据
        print("加载数据...")
        data = load_json.load_jsonl('./data/阿里云改写日志.json')
        log_data, error_data = spilit_log_message.split_log_pre_message(data)
        
        if len(log_data) == 0:
            print("没有有效数据")
            return
        
        # 创建输出目录
        output_dir = "k_comparison_analysis"
        os.makedirs(output_dir, exist_ok=True)
        
        # 比较不同的k值
        k_values = [3, 4, 5, 6, 7]
        results_summary = []
        
        for k in k_values:
            print(f"\n分析k={k}...")
            try:
                results = perform_kmeans_clustering(
                    log_data, 
                    n_clusters=k, 
                    max_features=300,  # 减少特征数以加快速度
                    random_state=42
                )
                
                # 记录结果摘要
                summary = {
                    'k': k,
                    'silhouette_score': results['silhouette_score'],
                    'n_samples': len(results['results_df'])
                }
                results_summary.append(summary)
                
                # 保存详细结果
                csv_path = os.path.join(output_dir, f'clustering_k{k}_results.csv')
                results['results_df'].to_csv(csv_path, index=False, encoding='utf-8-sig')
                
                print(f"k={k}: 轮廓系数={results['silhouette_score']:.4f}, 样本数={len(results['results_df'])}")
                
            except Exception as e:
                print(f"k={k}分析失败: {e}")
        
        # 保存比较结果
        if results_summary:
            import json
            summary_path = os.path.join(output_dir, 'k_comparison_summary.json')
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(results_summary, f, ensure_ascii=False, indent=2)
            
            print(f"\n比较结果已保存到: {summary_path}")
            
            # 找出最佳k值
            best_result = max(results_summary, key=lambda x: x['silhouette_score'])
            print(f"推荐的最佳k值: {best_result['k']} (轮廓系数: {best_result['silhouette_score']:.4f})")
        
    except ImportError as e:
        print(f"无法导入完整版模块: {e}")
        print("请安装必要的依赖包")

def main():
    """
    主函数：运行所有示例
    """
    print("K-means聚类分析使用示例")
    print("=" * 50)
    
    # 示例1：使用自定义输出路径
    try:
        output_dir = example_with_custom_output()
        if output_dir:
            print(f"\n示例1完成，输出目录: {output_dir}")
    except Exception as e:
        print(f"示例1执行失败: {e}")
    
    # 示例2：使用特定k值
    try:
        example_with_specific_k()
        print("\n示例2完成")
    except Exception as e:
        print(f"示例2执行失败: {e}")
    
    # 示例3：比较不同k值
    try:
        example_compare_different_k()
        print("\n示例3完成")
    except Exception as e:
        print(f"示例3执行失败: {e}")
    
    print("\n所有示例执行完成！")

if __name__ == "__main__":
    main()
