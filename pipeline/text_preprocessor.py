"""
文本预处理模块
专门处理电商客服对话中的文本清理和标准化
"""

import re
from typing import List, Dict, Set
from collections import defaultdict

class EcommerceTextPreprocessor:
    """电商客服文本预处理器"""
    
    def __init__(self):
        # 电商相关停用词
        self.ecommerce_stopwords = {
            # URL和链接相关
            'https', 'http', 'www', 'com', 'cn', 'html', 'goods', 'mobile',
            'yangkeduo', 'taobao', 'tmall', 'jd',
            
            # 页面相关
            '商品详情页', '详情页', '商品页', '产品页', '链接', '网址', '地址',
            '当前用户来自', '用户来自', '来自',
            
            # 常见无意义词
            '的', '了', '是', '在', '有', '和', '与', '或', '但', '而',
            '这', '那', '这个', '那个', '这些', '那些',
            '什么', '怎么', '如何', '为什么', '哪里', '哪个',
            
            # 电商平台特定词汇
            'goods_id', 'page_from', 'trace_mark', 'refer_ad', 'adinfo',
            'oak_rcto', 'oc_trace', 'oc_refer', 'oak_actype', 'oak_sub_actype',
            
            # 其他常见停用词
            '嗯', '哦', '啊', '呃', '额', '嗯嗯', '哦哦', '啊啊',
            '就是', '然后', '所以', '因为', '如果', '虽然', '但是',
        }
        
        # URL模式
        self.url_patterns = [
            r'https?://[^\s]+',  # 完整URL
            r'www\.[^\s]+',      # www开头的域名
            r'[a-zA-Z0-9.-]+\.com[^\s]*',  # .com域名
            r'goods\.html\?[^\s]*',  # 商品页面
            r'mobile\.yangkeduo\.com[^\s]*',  # 拼多多移动端
        ]
        
        # 商品ID模式
        self.product_id_patterns = [
            r'goods_id=\d+',
            r'商品ID[:：]\s*\d+',
            r'产品编号[:：]\s*\d+',
        ]
        
        # 无意义字符模式
        self.noise_patterns = [
            r'[^\u4e00-\u9fa5a-zA-Z0-9\s]',  # 非中文、英文、数字的字符
            r'\s+',  # 多个空格
            r'^\s+|\s+$',  # 首尾空格
        ]
    
    def clean_text(self, text: str) -> str:
        """清理文本"""
        if not text or not isinstance(text, str):
            return ""
        
        # 1. 移除URL
        cleaned_text = self.remove_urls(text)
        
        # 2. 移除商品ID等技术信息
        cleaned_text = self.remove_technical_info(cleaned_text)
        
        # 3. 移除噪声字符
        cleaned_text = self.remove_noise(cleaned_text)
        
        # 4. 移除停用词
        cleaned_text = self.remove_stopwords(cleaned_text)
        
        # 5. 标准化空格
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        
        return cleaned_text
    
    def remove_urls(self, text: str) -> str:
        """移除URL"""
        for pattern in self.url_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        return text
    
    def remove_technical_info(self, text: str) -> str:
        """移除技术信息"""
        for pattern in self.product_id_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        return text
    
    def remove_noise(self, text: str) -> str:
        """移除噪声字符"""
        # 保留中文、英文、数字和基本标点
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s，。！？；：""''（）【】]', ' ', text)
        return text
    
    def remove_stopwords(self, text: str) -> str:
        """移除停用词"""
        words = text.split()
        filtered_words = [word for word in words if word.lower() not in self.ecommerce_stopwords]
        return ' '.join(filtered_words)
    
    def extract_meaningful_content(self, text: str) -> Dict[str, any]:
        """提取有意义的内容"""
        result = {
            'cleaned_text': '',
            'urls': [],
            'product_ids': [],
            'keywords': [],
            'intent_indicators': []
        }
        
        if not text:
            return result
        
        # 提取URL
        for pattern in self.url_patterns:
            urls = re.findall(pattern, text, flags=re.IGNORECASE)
            result['urls'].extend(urls)
        
        # 提取商品ID
        for pattern in self.product_id_patterns:
            product_ids = re.findall(pattern, text, flags=re.IGNORECASE)
            result['product_ids'].extend(product_ids)
        
        # 清理文本
        result['cleaned_text'] = self.clean_text(text)
        
        # 提取关键词（长度大于1的词）
        words = result['cleaned_text'].split()
        result['keywords'] = [word for word in words if len(word) > 1]
        
        # 识别意图指示词
        result['intent_indicators'] = self.identify_intent_indicators(result['cleaned_text'])
        
        return result
    
    def identify_intent_indicators(self, text: str) -> List[str]:
        """识别意图指示词"""
        intent_keywords = {
            'price_inquiry': ['价格', '多少钱', '费用', '成本', '收费', '报价'],
            'purchase_intent': ['买', '购买', '下单', '付款', '拍下', '要'],
            'service_request': ['人工', '客服', '服务', '帮助', '咨询'],
            'product_inquiry': ['照片', '证件照', '名片', '尺寸', '规格', '材质'],
            'process_inquiry': ['怎么', '如何', '流程', '步骤', '方法'],
            'complaint': ['投诉', '不满意', '问题', '错误', '退货', '退款'],
            'praise': ['满意', '不错', '很好', '谢谢', '感谢']
        }
        
        found_intents = []
        text_lower = text.lower()
        
        for intent_type, keywords in intent_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                found_intents.append(intent_type)
        
        return found_intents
    
    def batch_process(self, texts: List[str]) -> List[Dict[str, any]]:
        """批量处理文本"""
        return [self.extract_meaningful_content(text) for text in texts]
    
    def analyze_text_quality(self, texts: List[str]) -> Dict[str, any]:
        """分析文本质量"""
        stats = {
            'total_texts': len(texts),
            'empty_texts': 0,
            'url_containing_texts': 0,
            'avg_length_before_cleaning': 0,
            'avg_length_after_cleaning': 0,
            'common_keywords': defaultdict(int),
            'intent_distribution': defaultdict(int)
        }
        
        total_length_before = 0
        total_length_after = 0
        
        for text in texts:
            if not text or not text.strip():
                stats['empty_texts'] += 1
                continue
            
            total_length_before += len(text)
            
            # 检查是否包含URL
            if any(re.search(pattern, text, re.IGNORECASE) for pattern in self.url_patterns):
                stats['url_containing_texts'] += 1
            
            # 处理文本
            processed = self.extract_meaningful_content(text)
            total_length_after += len(processed['cleaned_text'])
            
            # 统计关键词
            for keyword in processed['keywords']:
                stats['common_keywords'][keyword] += 1
            
            # 统计意图
            for intent in processed['intent_indicators']:
                stats['intent_distribution'][intent] += 1
        
        # 计算平均长度
        valid_texts = stats['total_texts'] - stats['empty_texts']
        if valid_texts > 0:
            stats['avg_length_before_cleaning'] = total_length_before / valid_texts
            stats['avg_length_after_cleaning'] = total_length_after / valid_texts
        
        # 转换为普通字典并排序
        stats['common_keywords'] = dict(sorted(stats['common_keywords'].items(), 
                                             key=lambda x: x[1], reverse=True)[:20])
        stats['intent_distribution'] = dict(stats['intent_distribution'])
        
        return stats


def main():
    """测试示例"""
    preprocessor = EcommerceTextPreprocessor()
    
    # 测试文本
    test_texts = [
        "https://mobile.yangkeduo.com/goods.html?goods_id=757014800226&_oak_rcto=YWIbQCGDk9LRHewyeyhlDKZPzQYAQ4wtCkhBkFcndYkb8I74YBka9yrvSadrnmotTxc&page_from=401",
        "当前用户来自 商品详情页 https://mobile.yangkeduo.com/goods.html?goods_id=765693482581",
        "人工客服不在线吗",
        "售后无忧-看规则退",
        "在哪点发照片",
        "可以了 提交了 谢谢",
        "OK啦"
    ]
    
    print("=== 文本预处理测试 ===")
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n{i}. 原文: {text}")
        result = preprocessor.extract_meaningful_content(text)
        print(f"   清理后: {result['cleaned_text']}")
        if result['urls']:
            print(f"   URL: {result['urls']}")
        if result['intent_indicators']:
            print(f"   意图: {result['intent_indicators']}")
    
    # 分析文本质量
    print("\n=== 文本质量分析 ===")
    quality_stats = preprocessor.analyze_text_quality(test_texts)
    print(f"总文本数: {quality_stats['total_texts']}")
    print(f"包含URL的文本: {quality_stats['url_containing_texts']}")
    print(f"清理前平均长度: {quality_stats['avg_length_before_cleaning']:.1f}")
    print(f"清理后平均长度: {quality_stats['avg_length_after_cleaning']:.1f}")
    print(f"意图分布: {quality_stats['intent_distribution']}")


if __name__ == "__main__":
    main()
