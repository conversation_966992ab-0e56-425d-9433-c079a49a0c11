"""
运行电商客服用户信息分析的主程序
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from customer_analysis import CustomerAnalyzer

def run_analysis_with_custom_output(data_path: str, output_name: str = None):
    """
    使用自定义输出目录运行分析
    
    Args:
        data_path: 数据文件路径
        output_name: 自定义输出目录名称
    """
    print("=== 电商客服用户信息分析 ===")
    
    # 创建自定义输出目录
    if output_name is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_name = f"customer_analysis_{timestamp}"
    
    print(f"将使用输出目录: {output_name}")
    
    try:
        # 创建分析器
        analyzer = CustomerAnalyzer(data_path)
        
        # 运行完整分析
        output_dir = analyzer.run_complete_analysis(output_name)
        
        print(f"\n✅ 分析完成！结果保存在: {output_dir}")
        
        # 显示关键指标摘要
        print_analysis_summary(analyzer)
        
        return output_dir
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        return None

def print_analysis_summary(analyzer: CustomerAnalyzer):
    """打印分析结果摘要"""
    print("\n" + "="*50)
    print("📊 分析结果摘要")
    print("="*50)
    
    results = analyzer.analysis_results
    
    # 用户行为摘要
    if 'user_behavior' in results:
        user_activity = results['user_behavior']['user_activity']
        print(f"👥 总用户数: {user_activity['total_users']}")
        print(f"💬 总对话数: {user_activity['total_conversations']}")
        print(f"📈 平均每用户对话数: {user_activity['avg_conversations_per_user']:.2f}")
        
        # 最活跃时段
        hourly_dist = user_activity['hourly_distribution']
        if hourly_dist:
            peak_hour = max(hourly_dist.items(), key=lambda x: x[1])
            print(f"🕐 最活跃时段: {peak_hour[0]}点 ({peak_hour[1]} 次咨询)")
    
    # 服务质量摘要
    if 'service_quality' in results:
        resolution = results['service_quality']['resolution_rate']
        satisfaction = results['service_quality']['satisfaction_metrics']
        print(f"✅ 问题解决率: {resolution['resolution_percentage']:.1f}%")
        print(f"😊 用户满意度: {satisfaction['satisfaction_score']:.2f}")
    
    # 业务洞察摘要
    if 'business_insights' in results:
        conversion = results['business_insights']['conversion_analysis']
        customer_value = results['business_insights']['customer_value']
        print(f"💰 转化率: {conversion['conversion_percentage']:.1f}%")
        print(f"🔄 回头客比例: {customer_value['repeat_rate_percentage']:.1f}%")
    
    print("="*50)

def compare_multiple_datasets():
    """比较多个数据集的分析结果"""
    print("\n=== 多数据集对比分析 ===")
    
    datasets = [
        '../data/807凌晨改写log.json',
        '../data/阿里云改写日志.json'
    ]
    
    results_summary = []
    
    for i, data_path in enumerate(datasets):
        if not os.path.exists(data_path):
            print(f"⚠️  数据文件不存在: {data_path}")
            continue
            
        print(f"\n分析数据集 {i+1}: {data_path}")
        
        try:
            analyzer = CustomerAnalyzer(data_path)
            analyzer.load_data()
            analyzer.analyze_user_behavior()
            analyzer.analyze_service_quality()
            analyzer.analyze_business_insights()
            
            # 提取关键指标
            results = analyzer.analysis_results
            summary = {
                'dataset': os.path.basename(data_path),
                'total_users': results['user_behavior']['user_activity']['total_users'],
                'total_conversations': results['user_behavior']['user_activity']['total_conversations'],
                'resolution_rate': results['service_quality']['resolution_rate']['resolution_percentage'],
                'satisfaction_score': results['service_quality']['satisfaction_metrics']['satisfaction_score'],
                'conversion_rate': results['business_insights']['conversion_analysis']['conversion_percentage'],
                'repeat_rate': results['business_insights']['customer_value']['repeat_rate_percentage']
            }
            results_summary.append(summary)
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    # 显示对比结果
    if results_summary:
        print("\n📊 数据集对比结果:")
        print("-" * 80)
        print(f"{'数据集':<20} {'用户数':<8} {'对话数':<8} {'解决率':<8} {'满意度':<8} {'转化率':<8} {'回头客率':<8}")
        print("-" * 80)
        
        for summary in results_summary:
            print(f"{summary['dataset']:<20} "
                  f"{summary['total_users']:<8} "
                  f"{summary['total_conversations']:<8} "
                  f"{summary['resolution_rate']:<8.1f}% "
                  f"{summary['satisfaction_score']:<8.2f} "
                  f"{summary['conversion_rate']:<8.1f}% "
                  f"{summary['repeat_rate']:<8.1f}%")

def analyze_specific_time_period():
    """分析特定时间段的数据"""
    print("\n=== 时间段分析示例 ===")
    
    # 这里可以添加时间过滤逻辑
    # 例如：只分析最近7天的数据
    print("💡 提示: 可以通过修改数据加载逻辑来分析特定时间段")
    print("例如：最近7天、工作日vs周末、特定促销期间等")

def main():
    """主函数"""
    print("电商客服用户信息分析工具")
    print("=" * 50)
    
    # 示例1：分析默认数据集
    print("\n1️⃣  分析默认数据集...")
    try:
        output_dir = run_analysis_with_custom_output('../data/807凌晨改写log.json')
        if output_dir:
            print(f"✅ 示例1完成，结果保存在: {output_dir}")
    except Exception as e:
        print(f"❌ 示例1失败: {e}")
    
    # 示例2：比较多个数据集
    print("\n2️⃣  多数据集对比分析...")
    try:
        compare_multiple_datasets()
        print("✅ 示例2完成")
    except Exception as e:
        print(f"❌ 示例2失败: {e}")
    
    # 示例3：时间段分析提示
    print("\n3️⃣  时间段分析...")
    try:
        analyze_specific_time_period()
        print("✅ 示例3完成")
    except Exception as e:
        print(f"❌ 示例3失败: {e}")
    
    print("\n🎉 所有示例执行完成！")
    print("\n📚 使用说明:")
    print("1. 修改数据文件路径来分析不同的数据集")
    print("2. 自定义输出目录名称")
    print("3. 根据需要调整分析参数")
    print("4. 查看生成的报告和图表了解详细结果")

if __name__ == "__main__":
    main()
