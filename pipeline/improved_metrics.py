"""
改进版的指标计算方法
提供更准确和全面的指标统计
"""

from collections import defaultdict
from typing import Dict, List, Any
import re

class ImprovedMetricsCalculator:
    """改进版指标计算器"""
    
    def __init__(self, log_data: List[Dict]):
        self.log_data = log_data
    
    def calculate_enhanced_resolution_rate(self) -> Dict[str, Any]:
        """增强版问题解决率计算"""
        
        # 扩展的解决指示词
        resolution_indicators = {
            'explicit_positive': ['谢谢', '解决了', '明白了', '清楚了', '懂了', '好的谢谢'],
            'implicit_positive': ['好的', '可以', 'ok', '行', '嗯嗯', '收到'],
            'satisfaction': ['满意', '不错', '很好', '挺好的', '可以的'],
            'completion': ['完成了', '搞定了', '弄好了', '处理好了']
        }
        
        total_conversations = len(self.log_data)
        resolution_scores = {
            'high_confidence': 0,  # 明确表示解决
            'medium_confidence': 0,  # 可能解决
            'low_confidence': 0,   # 基本满意
            'unresolved': 0        # 未解决
        }
        
        for item in self.log_data:
            try:
                user_request = item['message']['user_request'].lower()
                
                # 高置信度解决
                if any(word in user_request for word in resolution_indicators['explicit_positive']):
                    resolution_scores['high_confidence'] += 1
                # 中等置信度解决
                elif any(word in user_request for word in resolution_indicators['satisfaction']):
                    resolution_scores['medium_confidence'] += 1
                # 低置信度解决
                elif any(word in user_request for word in resolution_indicators['implicit_positive']):
                    resolution_scores['low_confidence'] += 1
                else:
                    resolution_scores['unresolved'] += 1
                    
            except (KeyError, AttributeError):
                resolution_scores['unresolved'] += 1
        
        # 计算不同级别的解决率
        high_resolution_rate = resolution_scores['high_confidence'] / total_conversations
        overall_resolution_rate = (resolution_scores['high_confidence'] + 
                                 resolution_scores['medium_confidence'] + 
                                 resolution_scores['low_confidence']) / total_conversations
        
        return {
            'resolution_breakdown': resolution_scores,
            'high_confidence_rate': high_resolution_rate * 100,
            'overall_resolution_rate': overall_resolution_rate * 100,
            'total_conversations': total_conversations,
            'confidence_distribution': {
                k: v/total_conversations*100 for k, v in resolution_scores.items()
            }
        }
    
    def calculate_enhanced_satisfaction(self) -> Dict[str, Any]:
        """增强版满意度计算"""
        
        # 情感词典
        sentiment_words = {
            'very_positive': ['非常满意', '很好', '太好了', '完美', '优秀'],
            'positive': ['满意', '不错', '好的', '可以', '谢谢'],
            'neutral': ['一般', '还行', '凑合'],
            'negative': ['不好', '差', '不满意', '有问题'],
            'very_negative': ['很差', '太差了', '非常不满意', '投诉']
        }
        
        sentiment_scores = defaultdict(int)
        total_conversations = len(self.log_data)
        
        for item in self.log_data:
            try:
                user_request = item['message']['user_request'].lower()
                
                # 按情感强度分类
                if any(word in user_request for word in sentiment_words['very_positive']):
                    sentiment_scores['very_positive'] += 1
                elif any(word in user_request for word in sentiment_words['positive']):
                    sentiment_scores['positive'] += 1
                elif any(word in user_request for word in sentiment_words['neutral']):
                    sentiment_scores['neutral'] += 1
                elif any(word in user_request for word in sentiment_words['negative']):
                    sentiment_scores['negative'] += 1
                elif any(word in user_request for word in sentiment_words['very_negative']):
                    sentiment_scores['very_negative'] += 1
                else:
                    sentiment_scores['no_sentiment'] += 1
                    
            except (KeyError, AttributeError):
                sentiment_scores['no_sentiment'] += 1
        
        # 计算加权满意度分数 (5分制)
        weighted_score = (
            sentiment_scores['very_positive'] * 5 +
            sentiment_scores['positive'] * 4 +
            sentiment_scores['neutral'] * 3 +
            sentiment_scores['negative'] * 2 +
            sentiment_scores['very_negative'] * 1 +
            sentiment_scores['no_sentiment'] * 3  # 中性处理
        ) / total_conversations
        
        return {
            'sentiment_breakdown': dict(sentiment_scores),
            'weighted_satisfaction_score': weighted_score,
            'positive_rate': (sentiment_scores['very_positive'] + sentiment_scores['positive']) / total_conversations * 100,
            'negative_rate': (sentiment_scores['negative'] + sentiment_scores['very_negative']) / total_conversations * 100,
            'sentiment_distribution': {k: v/total_conversations*100 for k, v in sentiment_scores.items()}
        }
    
    def calculate_enhanced_conversion_rate(self) -> Dict[str, Any]:
        """增强版转化率计算"""
        
        # 购买意向分级
        purchase_intent = {
            'high_intent': ['下单', '购买', '买了', '付款了', '已付款'],
            'medium_intent': ['想买', '考虑买', '准备买', '打算买'],
            'low_intent': ['看看', '了解', '咨询价格', '多少钱'],
            'price_inquiry': ['价格', '多少钱', '费用', '成本', '收费'],
            'process_inquiry': ['怎么买', '如何下单', '购买流程', '怎么付款']
        }
        
        intent_scores = defaultdict(int)
        total_conversations = len(self.log_data)
        
        for item in self.log_data:
            try:
                user_request = item['message']['user_request'].lower()
                
                # 按购买意向强度分类
                if any(word in user_request for word in purchase_intent['high_intent']):
                    intent_scores['high_intent'] += 1
                elif any(word in user_request for word in purchase_intent['medium_intent']):
                    intent_scores['medium_intent'] += 1
                elif any(word in user_request for word in purchase_intent['process_inquiry']):
                    intent_scores['process_inquiry'] += 1
                elif any(word in user_request for word in purchase_intent['price_inquiry']):
                    intent_scores['price_inquiry'] += 1
                elif any(word in user_request for word in purchase_intent['low_intent']):
                    intent_scores['low_intent'] += 1
                else:
                    intent_scores['no_intent'] += 1
                    
            except (KeyError, AttributeError):
                intent_scores['no_intent'] += 1
        
        # 计算不同级别的转化率
        high_conversion_rate = intent_scores['high_intent'] / total_conversations * 100
        potential_conversion_rate = (intent_scores['high_intent'] + 
                                   intent_scores['medium_intent'] + 
                                   intent_scores['process_inquiry']) / total_conversations * 100
        
        return {
            'intent_breakdown': dict(intent_scores),
            'high_conversion_rate': high_conversion_rate,
            'potential_conversion_rate': potential_conversion_rate,
            'price_inquiry_rate': intent_scores['price_inquiry'] / total_conversations * 100,
            'intent_distribution': {k: v/total_conversations*100 for k, v in intent_scores.items()}
        }
    
    def calculate_enhanced_customer_value(self) -> Dict[str, Any]:
        """增强版客户价值分析"""

        user_engagement = defaultdict(list)

        # 收集用户的所有对话时间 - 优先使用leyanBuyerId
        for item in self.log_data:
            try:
                # 优先使用leyanBuyerId，如果没有则使用buyerNick
                user_id = item['context'].get('leyanBuyerId') or item['context'].get('buyerNick')
                if not user_id:
                    continue
                timestamp = int(item['__time__'])
                user_engagement[user_id].append(timestamp)
            except (KeyError, ValueError):
                continue
        
        # 分析用户行为模式
        user_analysis = {}
        for user, timestamps in user_engagement.items():
            timestamps.sort()
            conversation_count = len(timestamps)
            
            # 计算时间跨度
            if conversation_count > 1:
                time_span = timestamps[-1] - timestamps[0]
                avg_interval = time_span / (conversation_count - 1) if conversation_count > 1 else 0
            else:
                time_span = 0
                avg_interval = 0
            
            user_analysis[user] = {
                'conversation_count': conversation_count,
                'time_span_days': time_span / (24 * 3600),
                'avg_interval_days': avg_interval / (24 * 3600),
                'is_repeat_customer': conversation_count > 1,
                'is_high_value': conversation_count >= 3
            }
        
        # 统计各类客户
        total_users = len(user_analysis)
        repeat_customers = sum(1 for u in user_analysis.values() if u['is_repeat_customer'])
        high_value_customers = sum(1 for u in user_analysis.values() if u['is_high_value'])
        
        # 客户分层
        customer_segments = {
            'one_time': sum(1 for u in user_analysis.values() if u['conversation_count'] == 1),
            'occasional': sum(1 for u in user_analysis.values() if 2 <= u['conversation_count'] <= 2),
            'regular': sum(1 for u in user_analysis.values() if 3 <= u['conversation_count'] <= 5),
            'loyal': sum(1 for u in user_analysis.values() if u['conversation_count'] > 5)
        }
        
        return {
            'total_users': total_users,
            'repeat_customer_rate': repeat_customers / total_users * 100 if total_users > 0 else 0,
            'high_value_customer_rate': high_value_customers / total_users * 100 if total_users > 0 else 0,
            'customer_segments': customer_segments,
            'segment_distribution': {k: v/total_users*100 for k, v in customer_segments.items()} if total_users > 0 else {},
            'avg_conversations_per_user': sum(len(timestamps) for timestamps in user_engagement.values()) / total_users if total_users > 0 else 0
        }
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """生成综合指标报告"""
        
        resolution_metrics = self.calculate_enhanced_resolution_rate()
        satisfaction_metrics = self.calculate_enhanced_satisfaction()
        conversion_metrics = self.calculate_enhanced_conversion_rate()
        customer_value_metrics = self.calculate_enhanced_customer_value()
        
        return {
            'resolution_analysis': resolution_metrics,
            'satisfaction_analysis': satisfaction_metrics,
            'conversion_analysis': conversion_metrics,
            'customer_value_analysis': customer_value_metrics,
            'summary': {
                'high_confidence_resolution_rate': resolution_metrics['high_confidence_rate'],
                'weighted_satisfaction_score': satisfaction_metrics['weighted_satisfaction_score'],
                'high_conversion_rate': conversion_metrics['high_conversion_rate'],
                'repeat_customer_rate': customer_value_metrics['repeat_customer_rate']
            }
        }


def main():
    """使用示例"""
    # 这里可以添加测试代码
    print("改进版指标计算器已准备就绪")
    print("使用方法:")
    print("calculator = ImprovedMetricsCalculator(log_data)")
    print("report = calculator.generate_comprehensive_report()")


if __name__ == "__main__":
    main()
