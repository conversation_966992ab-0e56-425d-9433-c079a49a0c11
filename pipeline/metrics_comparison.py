"""
指标对比分析脚本
对比原始指标和改进指标的差异
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from customer_analysis import CustomerAnalyzer
from improved_metrics import ImprovedMetricsCalculator
from message_prossor.utils import load_json, spilit_log_message
import json

def compare_metrics(data_path: str):
    """对比原始指标和改进指标"""
    
    print("=" * 60)
    print("📊 指标对比分析")
    print("=" * 60)
    
    # 加载数据
    print("正在加载数据...")
    raw_data = load_json.load_jsonl(data_path)
    log_data, error_data = spilit_log_message.split_log_pre_message(raw_data)
    print(f"成功加载 {len(log_data)} 条有效对话数据\n")
    
    # 原始指标计算
    print("🔍 计算原始指标...")
    analyzer = CustomerAnalyzer(data_path)
    analyzer.log_data = log_data
    
    original_service_quality = analyzer.analyze_service_quality()
    original_business_insights = analyzer.analyze_business_insights()
    
    # 改进指标计算
    print("🚀 计算改进指标...")
    improved_calculator = ImprovedMetricsCalculator(log_data)
    improved_report = improved_calculator.generate_comprehensive_report()
    
    # 对比展示
    print("\n" + "=" * 60)
    print("📈 指标对比结果")
    print("=" * 60)
    
    # 1. 问题解决率对比
    print("\n1️⃣ 问题解决率对比:")
    print("-" * 40)
    original_resolution = original_service_quality['resolution_rate']['resolution_percentage']
    improved_high = improved_report['resolution_analysis']['high_confidence_rate']
    improved_overall = improved_report['resolution_analysis']['overall_resolution_rate']
    
    print(f"原始方法: {original_resolution:.1f}%")
    print(f"改进方法 (高置信度): {improved_high:.1f}%")
    print(f"改进方法 (整体): {improved_overall:.1f}%")
    print(f"差异: {improved_overall - original_resolution:+.1f}%")
    
    # 详细分解
    resolution_breakdown = improved_report['resolution_analysis']['confidence_distribution']
    print("\n解决率详细分解:")
    for level, percentage in resolution_breakdown.items():
        print(f"  - {level}: {percentage:.1f}%")
    
    # 2. 用户满意度对比
    print("\n2️⃣ 用户满意度对比:")
    print("-" * 40)
    original_satisfaction = original_service_quality['satisfaction_metrics']['satisfaction_score']
    improved_satisfaction = improved_report['satisfaction_analysis']['weighted_satisfaction_score']
    
    print(f"原始方法: {original_satisfaction:.2f} (满意度评分)")
    print(f"改进方法: {improved_satisfaction:.2f} (5分制加权)")
    print(f"改进方法正面率: {improved_report['satisfaction_analysis']['positive_rate']:.1f}%")
    print(f"改进方法负面率: {improved_report['satisfaction_analysis']['negative_rate']:.1f}%")
    
    # 情感分解
    sentiment_breakdown = improved_report['satisfaction_analysis']['sentiment_distribution']
    print("\n情感分布详细分解:")
    for sentiment, percentage in sentiment_breakdown.items():
        print(f"  - {sentiment}: {percentage:.1f}%")
    
    # 3. 转化率对比
    print("\n3️⃣ 转化率对比:")
    print("-" * 40)
    original_conversion = original_business_insights['conversion_analysis']['conversion_percentage']
    improved_high_conversion = improved_report['conversion_analysis']['high_conversion_rate']
    improved_potential_conversion = improved_report['conversion_analysis']['potential_conversion_rate']
    
    print(f"原始方法: {original_conversion:.1f}%")
    print(f"改进方法 (高意向): {improved_high_conversion:.1f}%")
    print(f"改进方法 (潜在): {improved_potential_conversion:.1f}%")
    print(f"价格咨询率: {improved_report['conversion_analysis']['price_inquiry_rate']:.1f}%")
    
    # 意向分解
    intent_breakdown = improved_report['conversion_analysis']['intent_distribution']
    print("\n购买意向详细分解:")
    for intent, percentage in intent_breakdown.items():
        print(f"  - {intent}: {percentage:.1f}%")
    
    # 4. 回头客率对比
    print("\n4️⃣ 回头客率对比:")
    print("-" * 40)
    original_repeat_rate = original_business_insights['customer_value']['repeat_rate_percentage']
    improved_repeat_rate = improved_report['customer_value_analysis']['repeat_customer_rate']
    improved_high_value_rate = improved_report['customer_value_analysis']['high_value_customer_rate']
    
    print(f"原始方法: {original_repeat_rate:.1f}%")
    print(f"改进方法 (回头客): {improved_repeat_rate:.1f}%")
    print(f"改进方法 (高价值客户): {improved_high_value_rate:.1f}%")
    
    # 客户分层
    customer_segments = improved_report['customer_value_analysis']['segment_distribution']
    print("\n客户分层详细分解:")
    for segment, percentage in customer_segments.items():
        print(f"  - {segment}: {percentage:.1f}%")
    
    # 5. 总结和建议
    print("\n" + "=" * 60)
    print("💡 分析总结和建议")
    print("=" * 60)
    
    print("\n🔍 指标改进效果:")
    print(f"• 问题解决率: 从 {original_resolution:.1f}% 提升到 {improved_overall:.1f}% (+{improved_overall - original_resolution:.1f}%)")
    print(f"• 满意度评分: 从 {original_satisfaction:.2f} 提升到 {improved_satisfaction:.2f} (+{improved_satisfaction - original_satisfaction:.2f})")
    print(f"• 转化率识别: 从 {original_conversion:.1f}% 细化为高意向 {improved_high_conversion:.1f}% + 潜在 {improved_potential_conversion:.1f}%")
    print(f"• 客户价值: 从简单回头客 {original_repeat_rate:.1f}% 扩展到多层次分析")
    
    print("\n📈 业务洞察:")
    if improved_overall > original_resolution * 1.5:
        print("• 实际问题解决率比原始计算高，说明客服效果不错")
    if improved_satisfaction > 3.0:
        print("• 用户满意度处于中等偏上水平")
    if improved_potential_conversion > improved_high_conversion * 2:
        print("• 有大量潜在转化机会，可以加强引导")
    if improved_high_value_rate > 20:
        print("• 高价值客户比例较高，客户忠诚度好")
    
    print("\n🎯 优化建议:")
    print("• 扩展问题解决指示词，提高识别准确性")
    print("• 建立更细致的情感分析模型")
    print("• 针对不同购买意向制定差异化策略")
    print("• 建立客户分层管理体系")
    
    return {
        'original_metrics': {
            'resolution_rate': original_resolution,
            'satisfaction_score': original_satisfaction,
            'conversion_rate': original_conversion,
            'repeat_rate': original_repeat_rate
        },
        'improved_metrics': {
            'resolution_rate': improved_overall,
            'satisfaction_score': improved_satisfaction,
            'conversion_rate': improved_potential_conversion,
            'repeat_rate': improved_repeat_rate
        },
        'detailed_analysis': improved_report
    }

def save_comparison_report(comparison_results: dict, output_path: str = "metrics_comparison_report.json"):
    """保存对比分析报告"""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(comparison_results, f, ensure_ascii=False, indent=2)
    print(f"\n📄 详细对比报告已保存到: {output_path}")

def main():
    """主函数"""
    data_path = '../data/807凌晨改写log.json'
    
    if not os.path.exists(data_path):
        print(f"❌ 数据文件不存在: {data_path}")
        return
    
    # 执行对比分析
    comparison_results = compare_metrics(data_path)
    
    # 保存报告
    save_comparison_report(comparison_results)
    
    print("\n✅ 指标对比分析完成！")

if __name__ == "__main__":
    main()
